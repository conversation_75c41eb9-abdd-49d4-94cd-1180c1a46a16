import type { ServerType } from '@/enums'
import { http } from '@/http/http'

// 参数接口
export interface LoginParams {
  code: string
  dataCode?: string
  fromTo?: number
}

// 响应接口
export interface LoginRes {
  data: {
    authorization: string
    inBlacklist: boolean
    userCode: string
    userId: string
    realName: string
  }
  errCode: string
  errMessage: string
  success: boolean
}

// 微信登录
export const wxParentsUserLoginPath = '/wxparents/user/login'
// 抖音登录
export const wxparentsUserDouYinLogin = '/wxparents/user/douYinLogin'

// 参数接口
export interface GetPhoneParams {
  code: string
  dataCode?: string
  fromTo?: number
}

// 响应接口
export interface GetPhoneRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}

export function wxparentsUserGetPhoneApi(params: GetPhoneParams) {
  return http.post<GetPhoneRes>('/wxparents/user/getPhone', params)
}

// 参数接口
export interface SignUrlLinkParams {
  query: string
  userInfoId: number
}

// 响应接口
export interface SignUrlLinkRes {
  data: string
  errCode: string
  errMessage: string
  success: boolean
}

/**
 * 小程序来源统计
 * @param {object} params cmd
 * @param {string} params.query 生成参数
 * @returns
 */
export function wxmalinkSignUrlLinkApi(params: SignUrlLinkParams) {
  return http.post<SignUrlLinkRes>('/wxmalink/signUrlLink', params, {}, true)
}

// 参数接口
export interface GetUserServerParams {
  certificationId?: number
  userId: number
}

// 响应接口
export interface GetUserServerRes {
  data: {
    barCodeCardNum: string
    barCodeCardPassword: string
    changeAuth: boolean
    changeOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    dataCode: string
    existUncompletedChange: boolean
    existUncompletedRegister: boolean
    existUncompletedRenewal: boolean
    phone: string
    qrAuth: boolean
    qrOrderInfo: {
      capacity: number
      certificationId: number
      createdDate: string
      expiryDate: string
      flow: number
      orderCode: string
      startDate: string
      tempName: string
      tempType: number
    }
    registerAuth: boolean
    registerOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    renewalAuth: boolean
    renewalInformDTO: {
      companyName: string
      gsDataId: number
      isNeedInform: boolean
      validDate: string
      vendorCode: string
    }
    renewalOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    reportAuth: boolean
    reportOrderInfo: {
      createdDate: string
      orderCode: string
      orderContent: string
      serverType: ServerType
    }
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
export function getUserServerInfoApi(params: GetUserServerParams) {
  return http.post<GetUserServerRes>('/user/getUserServerInfo', params)
}

// 参数接口
export interface UpdateUserPhoneParams {
  barCodeCardNum?: string
  barCodeCardPassword?: string
  phone: string
  userId: number
}

// 响应接口
export interface UpdateUserPhoneRes {
  data: {
    barCodeCardNum: string
    barCodeCardPassword: string
    phone: string
    realName: string
    userCode: string
    userId: number
  }
  errCode: string
  errMessage: string
  success: boolean
}
/**
 * 修改用户手机号，条码卡
 * @param {object} params cmd
 * @param {string} params.barCodeCardNum 用户条码卡账号/手机端的手机号
 * @param {string} params.barCodeCardPassword 用户条码卡密码/手机端对应的密码
 * @param {string} params.phone 电话
 * @param {number} params.userId 用户id
 * @returns
 */
export function updateUserPhoneApi(params: UpdateUserPhoneParams) {
  return http.post<UpdateUserPhoneRes>('/user/updateUserPhone', params)
}
